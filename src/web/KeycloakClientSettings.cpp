#include "web/KeycloakClientSettings.h"

using namespace security;

const JsonSchema KeycloakTokenResponseSchema =
  JsonSchema({ { "access_token", JsonSchema(json::value_t::string, "The JWT") },
               { "expires_in", JsonSchema(json::value_t::number_unsigned, "Number of seconds before the token is invalid") },
               { "refresh_token", JsonSchema(json::value_t::string, "The refresh token") },
               { "refresh_expires_in", JsonSchema(json::value_t::number_unsigned, "Number of seconds before the refresh token becomes invalid") },
               { "token_type", JsonSchema(json::value_t::string, "The type of this JWT").SetPossibleVals({ KEYCLOAK_TOKEN_TYPE }) } });
KeycloakToken KeycloakToken::FromJSON(const json& val)
{
	KeycloakTokenResponseSchema.TestThrow(val);
	KeycloakToken ret;
	ret.Token = val["access_token"].get<std::string>();
	ret.RefreshToken = val["refresh_token"].get<std::string>();
	ret.Expire = std::chrono::system_clock::now() + std::chrono::seconds(val["expires_in"].get<uint32_t>());
	ret.RefreshExpire = std::chrono::system_clock::now() + std::chrono::seconds(val["refresh_expires_in"].get<uint32_t>());
	return ret;
}

boost::future<web::http::response> KeycloakClientAuthenticationMode::RequestToken(const KeycloakClientSettings& settings) const
{
	web::WebClient client(web::websockets::uri(settings.Address));
	ConfigureClient(client);
	client.CertAuthorities = settings.CertificateAuthorities;
	web::QueryString params { GetParameters() };
	params["client_id"] = settings.ClientID;
	if (!settings.ClientSecret.empty())
		params["client_secret"] = settings.ClientSecret;
	if (!settings.Scope.empty())
		params["scope"] = settings.Scope;
	return client.Request(web::http::verb::post, std::format("/realms/{}/protocol/openid-connect/token", settings.Realm), params.ToString(),
	                      "application/x-www-form-urlencoded");
}

web::QueryString KeycloakRefreshTokenAuthentication::GetParameters() const
{
	return { { "grant_type", "refresh_token" }, { "refresh_token", RefreshToken } };
}

web::QueryString KeycloakUsernamePasswordAuthentication::GetParameters() const
{
	return { { "username", Username }, { "password", Password }, { "grant_type", "password" } };
}

KeycloakCertificateAuthentication::KeycloakCertificateAuthentication(const web::FCertificates& certs) : web::FCertificates(certs) {}

void KeycloakCertificateAuthentication::ConfigureClient(web::WebClient& client) const
{
	client.Certificates = static_cast<const FCertificates&>(*this);
}

web::QueryString KeycloakCertificateAuthentication::GetParameters() const
{
	return { { "grant_type", "password" } };
}

KeycloakToken KeycloakClientSettings::RequestBearerToken(const std::unique_ptr<KeycloakClientAuthenticationMode>& authModeOverride) const
{
	const std::unique_ptr<KeycloakClientAuthenticationMode>& authModeToUse = authModeOverride ? authModeOverride : AuthMode;
	if (!authModeToUse)
		return {};

	if (!Address.get_valid() || !Address.is_absolute() || Address.get_type() != websocketpp::uri::http)
		throw std::runtime_error("Invalid Keycloak address");

	const web::http::response response = authModeToUse->RequestToken(*this).get();
	if (response.get_error_code())
		throw std::runtime_error(std::format("Failed to get token from Keycloak at {}: {}", Address.str(), response.get_error_code().message()));

	if (response.get_status_code() != web::http::status::ok)
		throw std::runtime_error(std::format("Keycloak at {} rejected token request: {}", Address.str().c_str(), response.get_status_msg()));

	json responseJson;
	try
	{
		responseJson = json::parse(response.get_body());
	}
	catch (const std::exception&)
	{
		throw std::runtime_error("Keycloak response to token request is not a valid JSON");
	}

	try
	{
		return KeycloakToken::FromJSON(responseJson);
	}
	catch (const SchemaError& err)
	{
		throw std::runtime_error(std::format("Keycloak token response is invalid: {}", err.what()));
	}
}

bool KeycloakClientSettings::RefreshToken(const KeycloakToken& currentToken, KeycloakToken& outToken, bool forceRefresh) const
{
	if (currentToken.RefreshToken.empty())
		throw std::runtime_error("Refresh token is not valid!");

	auto now = std::chrono::system_clock::now();
	if (!forceRefresh && now + TokenRefreshMargin < currentToken.Expire)
		return false;

	if (now > currentToken.RefreshExpire)
		throw std::runtime_error("Refresh token is expired!");

	outToken = RequestBearerToken(std::make_unique<KeycloakRefreshTokenAuthentication>(currentToken.RefreshToken));

	return true;
}

KeycloakToken KeycloakClientSettings::ObtainOrRefreshToken(const KeycloakToken& currentToken, bool forceRefresh, const LogCategory* logCategory) const noexcept
{
	KeycloakToken ret;
	if (!currentToken.RefreshToken.empty())
	{
		try
		{
			security::KeycloakToken refreshedToken;
			if (RefreshToken(currentToken, refreshedToken, forceRefresh))
			{
				if (logCategory)
					Logger::Log(*logCategory, Info, "Access token successfully refreshed!");
				return refreshedToken;
			}
		}
		catch (const std::exception& e)
		{
			if (logCategory)
			{
				if (AuthMode)
					Logger::Log(*logCategory, Warning, "Failure refreshing access token - %s. Will attempt a new login instead.", e.what());
				else
					Logger::Log(*logCategory, Error, "Failure refreshing access token - %s", e.what());
			}
		}
	}

	try
	{
		ret = RequestBearerToken();
	}
	catch (const std::exception& e)
	{
		if (logCategory)
			Logger::Log(*logCategory, Error, "Failure getting access token: %s", e.what());
	}

	return ret;
}
