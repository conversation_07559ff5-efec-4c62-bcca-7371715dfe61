#include "web/WebClient.h"

#include "YUtils.h"

using namespace web;

const JsonSchema ClientConfig =
  JsonSchema({ { "tls", JsonSchema({ { "cert-authorities", JsonSchema(json::value_t::array, "List of paths to CA certificates", json::array({}))
                                                             .SetChildSchema(JsonSchema(json::value_t::string, "A filepath to the CA certificates")) } }) } });

WebClient::WebClient(const std::string& host, bool tls) : WebEndpoint(), bIsSecure(tls), HostAddress(host)
{
	Schema() += ClientConfig;

	TLSMethod = boost::asio::ssl::context::tls_client;
	TLSVerifyMode = boost::asio::ssl::context::verify_peer;
}

WebClient::~WebClient()
{
	Stop();

	ScopedLock lock(mInFlightRequests);
	mInFlightRequestsCV.wait(lock, [this]() -> bool { return mInFlightRequests->empty(); });
}

websockets::uri WebClient::MakeAbsoluteURI_HTTP(const std::string& resource) const
{
	websockets::uri requestUri(resource);
	if (requestUri.is_absolute())
		return requestUri;

	websockets::uri targetUri(GetAddressHTTP());
	if (!requestUri.is_relative() || requestUri.get_resource() == "/")
		return targetUri;

	if (!targetUri.get_query().empty())
		throw std::runtime_error("Cannot append a request path to a base path which has a query - " + targetUri.str());

	std::string ret(targetUri.str());
	if (ret.ends_with('/'))
		ret += requestUri.get_resource().substr(1);
	else
		ret += requestUri.get_resource();
	return websockets::uri(ret);
}

boost::future<http::response> WebClient::Request(const websockets::uri& uri, http::request request, const std::optional<uint64_t>& timeoutMs)
{
	if (uri.get_type() != websockets::uri::http)
	{
		boost::promise<http::response> promise;
		promise.set_value(websockets::error::make_error_code(websockets::error::invalid_uri));
		return promise.get_future();
	}

	if (!AcceptedEncodings.empty() && !request.get_accepted_encodings().has_value())
		request.set_accepted_encodings(AcceptedEncodings);

	if (AuthorizationHeader && !request.get_headers().contains(web::http::header(web::http::field::authorization)))
		request.append_header(web::http::header(web::http::field::authorization), *AuthorizationHeader);

	std::unique_ptr<websockets::imaxa_endpoint> endpointToUse;
	if (Mode == EWebClientMode::SingleInstancePersisting)
	{
		LazyInit();
		return DoRequestOnEndpoint(*Endpoint, uri, std::move(request), timeoutMs.value_or(TimeoutMs));
	}
	else
	{
		std::shared_ptr<boost::promise<http::response>> promise = std::make_shared<boost::promise<http::response>>();
		{
			ScopedLock lock(mInFlightRequests);
			mInFlightRequests->insert(promise);
		}
		std::thread worker([this, promise, uri, request = std::move(request), timeout = timeoutMs.value_or(TimeoutMs)]() {
			std::unique_ptr<websockets::imaxa_endpoint> newEndpoint;
			OnInitialize(newEndpoint);
			std::error_code ec;
			newEndpoint->init_asio(ec);
			if (ec)
			{
				promise->set_value(ec);
				return;
			}

			auto future = DoRequestOnEndpoint(*newEndpoint, uri, std::move(request), timeout);
			newEndpoint->run();
			promise->set_value(future.get());
			{
				ScopedLock lock(mInFlightRequests);
				mInFlightRequests->erase(promise);
			}
			mInFlightRequestsCV.notify_one();
		});
		worker.detach();
		return promise->get_future();
	}
}

void WebClient::OnInitialize(std::unique_ptr<websockets::imaxa_endpoint>& e)
{
	if (bIsSecure)
		e = std::make_unique<websockets::imaxa_client_endpoint<websockets::config::imaxa_config_tls>>();
	else
		e = std::make_unique<websockets::imaxa_client_endpoint<websockets::config::imaxa_config>>();

	WebEndpoint::OnInitialize(e);
}

void WebClient::on_create_tls_context(boost::asio::ssl::context& ctx) const
{
	WebEndpoint::on_create_tls_context(ctx);
	ctx.set_default_verify_paths();
	for (const std::filesystem::path& cert : CertAuthorities) ctx.load_verify_file(cert.string());
}

boost::future<std::pair<http::status::value, std::error_code>> WebClient::DownloadFile(const websockets::uri& uri, std::ostream& outContent,
                                                                                       const std::function<void(size_t, size_t)>& progressHandler)
{
	if (uri.get_type() != websockets::uri::http)
	{
		boost::promise<std::pair<http::status::value, std::error_code>> promise;
		promise.set_value({ http::status::uninitialized, websockets::error::make_error_code(websockets::error::invalid_uri) });
		return promise.get_future();
	}

	std::shared_ptr<boost::promise<http::response>> promise = std::make_shared<boost::promise<http::response>>();
	{
		ScopedLock lock(mInFlightRequests);
		mInFlightRequests->insert(promise);
	}
	std::thread worker([this, promise, uri, &outContent, progressHandler]() {
		std::unique_ptr<websockets::imaxa_endpoint> newEndpoint;
		OnInitialize(newEndpoint);
		std::error_code ec;
		newEndpoint->init_asio(ec);
		if (ec)
		{
			promise->set_value(ec);
			return;
		}

		if (uri.get_secure() && !newEndpoint->is_secure())
		{
			promise->set_value(websockets::error::make_error_code(websockets::error::endpoint_not_secure));
			return;
		}

		imaxa_connection_ptr con = dynamic_cast<websockets::imaxa_client_interface*>(newEndpoint.get())->get_connection(uri, ec);
		if (ec)
		{
			promise->set_value(ec);
			return;
		}
		con->set_max_http_body_size(0);
		con->set_http_response_timeout(0);

		con->set_http_handler([promise](imaxa_connection_hdl_ref hdl) {
			imaxa_connection_ptr con = hdl.lock();
			if (con)
				promise->set_value({ con->take_response(), con->get_ec() });
			else
				promise->set_value(websockets::error::make_error_code(websockets::error::bad_connection));
		});

		con->set_progress_handler([&outContent, progressHandler](imaxa_connection_hdl_ref hdl, size_t completed, size_t total) {
			auto con = hdl.lock();
			outContent << con->get_response().get_body();
			con->consume_response_body();

			if (progressHandler)
				progressHandler(completed, total);
		});

		dynamic_cast<websockets::imaxa_client_interface*>(newEndpoint.get())->connect(con);

		newEndpoint->run();
		{
			ScopedLock lock(mInFlightRequests);
			mInFlightRequests->erase(promise);
		}
		mInFlightRequestsCV.notify_one();
	});
	worker.detach();
	return promise->get_future().then([](boost::future<http::response> fut) -> std::pair<http::status::value, std::error_code> {
		const http::response response { fut.get() };
		return { response.get_status_code(), response.get_error_code() };
	});
}

imaxa_connection_ptr WebClient::NewWebsocketConnection(const std::string& resource, std::error_code& ec)
{
	if (!Initialized())
	{
		ec = websockets::error::make_error_code(websockets::error::endpoint_unavailable);
		return {};
	}

	websockets::uri uri;
	if (resource.empty() || resource.starts_with('/'))
		uri = websockets::uri(GetAddressWS() + resource);
	else
		uri = websockets::uri(resource);

	if (uri.get_type() != websockets::uri::websocket)
	{
		ec = websockets::error::make_error_code(websockets::error::invalid_uri);
		return {};
	}

	if (uri.get_secure() && !Endpoint->is_secure())
	{
		ec = websockets::error::make_error_code(websockets::error::endpoint_not_secure);
		return {};
	}

	return dynamic_cast<websockets::imaxa_client_interface*>(Endpoint.get())->get_connection(uri, ec);
}

void WebClient::AddWebsocketConnection(const imaxa_connection_ptr& con)
{
	dynamic_cast<websockets::imaxa_client_interface*>(Endpoint.get())->connect(con);
}

boost::future<http::response> WebClient::DoRequestOnEndpoint(websockets::imaxa_endpoint& endpoint, const websockets::uri& uri, http::request request, uint64_t timeoutMs)
{
	if (uri.get_secure() && !endpoint.is_secure())
	{
		boost::promise<http::response> promise;
		promise.set_value(websockets::error::make_error_code(websockets::error::endpoint_not_secure));
		return promise.get_future();
	}

	std::error_code ec;
	imaxa_connection_ptr con = dynamic_cast<websockets::imaxa_client_interface&>(endpoint).get_connection(uri, ec);
	if (ec)
	{
		boost::promise<http::response> promise;
		promise.set_value(ec);
		return promise.get_future();
	}

	con->set_request(std::move(request), ec);
	if (ec)
	{
		boost::promise<http::response> promise;
		promise.set_value(ec);
		return promise.get_future();
	}

	auto promisePtr = std::make_shared<boost::promise<http::response>>();
	con->set_http_response_timeout(timeoutMs);
	con->set_http_handler([promisePtr](imaxa_connection_hdl_ref hdl) {
		imaxa_connection_ptr con = hdl.lock();
		if (con)
			promisePtr->set_value({ con->take_response(), con->get_ec() });
		else
			promisePtr->set_value(websockets::error::make_error_code(websockets::error::bad_connection));
	});

	dynamic_cast<websockets::imaxa_client_interface&>(endpoint).connect(con);
	return promisePtr->get_future();
}

void WebClient::PreRun()
{
	WebEndpoint::PreRun();

	Endpoint->start_perpetual();
}

void WebClient::OnConfigLoaded(const std::filesystem::path& filename)
{
	WebEndpoint::OnConfigLoaded(filename);

	for (const json& val : GetConfig("tls.cert-authorities"))
	{
		std::filesystem::path certPath = val.get<std::string>();
		if (certPath.empty())
			continue;
		if (certPath.is_relative())
			certPath = mTLSSharedDirectory / certPath;
		CertAuthorities.push_back(certPath);
	}
}
