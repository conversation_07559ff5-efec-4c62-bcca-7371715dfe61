#include "DetectedCardsEventDto.h"

#include <Timing.h>

#include "EsySharedTypes.h"

json CardDto::ToJSON() const
{
    json root(json::value_t::object);
    root["cardName"] = CardName;
    root["confidence"] = Confidence;
    root["x"] = X;
    root["y"] = Y;
    root["width"] = Width;
    root["height"] = Height;
    if (!RegionId.empty()) {
        root["regionId"] = RegionId;
    }
    return root;
}

DetectedCardsEventDto::DetectedCardsEventDto(const std::vector<CardDto>& cards) : Cards(cards)
{
}

json DetectedCardsEventDto::ToJSON() const
{
    json root(json::value_t::object);
    json cards(json::value_t::array);
    for (const auto& card : Cards) cards.push_back(card.ToJSON());
    root["cards"] = std::move(cards);
    return root;
}

json DetectedCardsEventDto::ToEvent() const
{
    json root(json::value_t::object);
    root["name"] = esy::event::DetectedCardsEventName;
    root["data"] = ToJSON();
    root["triggered"] = ytime::GetSystemTimeMsec();;
    return root;
}
