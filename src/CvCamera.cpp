#include "CvCamera.h"

#include <opencv2/videoio.hpp>

#include "CvProcessor.h"

DEFINE_LOG_CATEGORY(LogCvCamera, "esy-camera")

void CvCamera::Release()
{
    stopFlag.store(true);
}

void CvCamera::SetRegions(const std::vector<RegionConfig>& newRegions)
{
    regions = newRegions;
    LOG(LogCvCamera, Info, "Set %zu regions for camera processing", regions.size());
}

ECvProcessor CvCamera::ConnectAndCapture(const std::string &rtspUrl, const std::shared_ptr<EsyClientMng> &clientMng, CvCard& cvCard) const
{
    LOG(LogCvCamera, Important, "[%s] Connecting to camera %s...", CvProcessor::GetThreadName().c_str(), rtspUrl.c_str());

    cv::VideoCapture cap(rtspUrl);

    if (!cap.isOpened()) {
        LOG(LogCvCamera, Error, "Could not open camera %s %s", rtspUrl.c_str(), cv::getBuildInformation().c_str());
        return ECvProcessor::Failed;
    }

    cv::Mat frame;
    int frameCount{};
    constexpr int skipFrames{10}; // Capture every 10th frame
    
    // If we have regions defined, log them
    if (!regions.empty()) {
        LOG(LogCvCamera, Info, "Processing stream with %d defined regions", regions.size());
    }
    
    // Continue with existing frame processing logic, but now with region support
    // ...
    
    // When processing frames, you can now check regions:
    while (cap.read(frame) && !stopFlag.load()) {
        if (++frameCount % skipFrames != 0) {
            continue;
        }
        
        // Process the whole frame as before
        std::vector<int> classIds;
        std::vector<float> confidences;
        std::vector<cv::Rect> boxes;
        std::vector<std::vector<float>> scores_all;
        
        cvCard.DetectCard(frame, CvCard::ConfThreshold, CvCard::NmsThreshold, 
                          classIds, confidences, boxes, scores_all);
        
        // If regions are defined, process each region separately
        if (!regions.empty()) {
            for (const auto& region : regions) {
                // Define region of interest
                cv::Rect roi(region.x, region.y, region.width, region.height);
                
                // Make sure ROI is within frame boundaries
                roi = roi & cv::Rect(0, 0, frame.cols, frame.rows);
                
                if (roi.width > 0 && roi.height > 0) {
                    // Extract region
                    cv::Mat regionMat = frame(roi);
                    
                    // Process region
                    std::vector<int> regionClassIds;
                    std::vector<float> regionConfidences;
                    std::vector<cv::Rect> regionBoxes;
                    std::vector<std::vector<float>> regionScores;
                    
                    cvCard.DetectCard(regionMat, CvCard::ConfThreshold, CvCard::NmsThreshold, 
                                     regionClassIds, regionConfidences, regionBoxes, regionScores);
                    
                    // Adjust bounding box coordinates to be relative to the full frame
                    for (auto& box : regionBoxes) {
                        box.x += region.x;
                        box.y += region.y;
                    }
                    
                    // Add region ID to the detection results
                    // This would require extending your event data structure to include region IDs
                    
                    // Merge region results with full frame results if needed
                    // ...
                }
            }
        } else {
            std::vector<int> classIds; // Zero based class ids
            std::vector<float> confidences; // Confidence values
            std::vector<cv::Rect> boxes; // Bounding boxes
            std::vector<std::vector<float>> scores_all;
            cvCard.DetectCard(frame, CvCard::ConfThreshold, CvCard::NmsThreshold, classIds, confidences, boxes, scores_all);

            LOG(LogCvCamera, Info, "Frame: %d", frameCount);
            json jsonEvent = cvCard.GetPredictions(classIds, confidences, boxes, "");
            if (jsonEvent != nullptr)
            {
                clientMng->Broadcast(jsonEvent);
            }
            // Press 'q' to exit the loop
            if ((cv::waitKey(30) >= 0) || stopFlag.load()) {
                LOG(LogCvCamera, Important, "[%s] Exiting camera %s stream", CvProcessor::GetThreadName().c_str(), rtspUrl.c_str());
                break;
            }
        }
        
        // Continue with existing event sending logic
        // ...
    }
    
    return ECvProcessor::Completed;
}
