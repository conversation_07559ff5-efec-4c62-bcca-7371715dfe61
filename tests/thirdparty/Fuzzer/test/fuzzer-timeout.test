RUN: not LLVMFuzzer-TimeoutTest -timeout=1 2>&1 | FileCheck %s --check-prefix=TimeoutTest
TimeoutTest: ALARM: working on the last Unit for
TimeoutTest: Test unit written to ./timeout-
TimeoutTest: == ERROR: libFuzzer: timeout after
TimeoutTest: #0
TimeoutTest: #1
TimeoutTest: #2
TimeoutTest: SUMMARY: libFuzzer: timeout

RUN: not LLVMFuzzer-TimeoutTest -timeout=1 %S/hi.txt 2>&1 | FileCheck %s --check-prefix=SingleInputTimeoutTest
SingleInputTimeoutTest: ALARM: working on the last Unit for {{[1-3]}} seconds
SingleInputTimeoutTest-NOT: Test unit written to ./timeout-

RUN: LLVMFuzzer-TimeoutTest -timeout=1 -timeout_exitcode=0

RUN: not LLVMFuzzer-TimeoutEmptyTest -timeout=1 2>&1 | FileCheck %s --check-prefix=TimeoutEmptyTest
TimeoutEmptyTest: ALARM: working on the last Unit for
TimeoutEmptyTest: == ERROR: libFuzzer: timeout after
TimeoutEmptyTest: SUMMARY: libFuzzer: timeout
