# Multiple Detection Regions Support

This document describes the implementation of multiple detection regions within a single camera frame for the EyeSeeYou card detection system.

## Overview

The multiple regions feature allows you to define specific areas within a camera frame where card detection should be performed. This is useful for:

- Focusing detection on specific table areas (dealer area, player areas, community cards)
- Reducing false positives from irrelevant parts of the frame
- Improving performance by processing smaller regions
- Providing region-specific detection results

## Configuration

### Stream Configuration File

Use the `detect-stream-config` command with a JSON configuration file that includes regions:

```json
{
  "rtspUrl": "rtsp://user:password@camera-ip:554/stream",
  "regions": [
    {
      "id": "dealer_area",
      "x": 100,
      "y": 50,
      "width": 800,
      "height": 600
    },
    {
      "id": "player_area",
      "x": 1000,
      "y": 400,
      "width": 700,
      "height": 500
    }
  ]
}
```

### Region Properties

- `id`: Unique identifier for the region (string)
- `x`: X coordinate of the top-left corner (integer)
- `y`: Y coordinate of the top-left corner (integer)
- `width`: Width of the region in pixels (integer)
- `height`: Height of the region in pixels (integer)

## Usage

### Command Line

```bash
./eye-see-you detect-stream-config config.json model.cfg model.weights classes.names
```

### Example Configurations

See the example configuration files:
- `data/detect-stream-conf.json` - Basic multi-region setup
- `data/detect-stream-multi-regions-example.json` - Advanced multi-region setup

## Output Format

When regions are defined, the detection results include region information:

```json
{
  "name": "DetectedCards",
  "data": {
    "cards": [
      {
        "cardName": "AS",
        "confidence": 0.95,
        "x": 150,
        "y": 100,
        "width": 50,
        "height": 70,
        "regionId": "dealer_area"
      }
    ]
  },
  "triggered": 1640995200000
}
```

## Implementation Details

### Region Processing

1. Each defined region is extracted from the full frame
2. Card detection is performed on each region separately
3. Bounding box coordinates are adjusted to be relative to the full frame
4. Results include the region ID for identification

### Backward Compatibility

- If no regions are defined, the system processes the entire frame
- Existing code without region support continues to work
- Region ID is optional in the output (empty string if not specified)

## Performance Considerations

- Multiple small regions can be faster than processing the entire frame
- Overlapping regions may produce duplicate detections
- Region boundaries should be carefully chosen to avoid cutting off cards
