#pragma once

#include <SDL2/SDL_ttf.h>

#include <map>
#include <string>

#include "gui/font.hpp"
#include "gui/image.hpp"

typedef std::shared_ptr<class SDLTrueTypeFont> FontPtr;

/**
 * SDL True Type Font implementation of Font. It uses the SDL_ttf library
 * to display True Type Fonts with SDL.
 *
 * NOTE: You must initialize the SDL_ttf library before using this
 *       class. Also, remember to call the SDL_ttf libraries quit
 *       function.
 *
 * Original author of this class is <PERSON><PERSON>. Some modifications
 * made by the Guichan team.
 */
class SDLTrueTypeFont : public BaseFont
{
   protected:
	class StaticFontLoader : public std::map<std::pair<std::string, int>, std::weak_ptr<SDLTrueTypeFont>>
	{
	   protected:
		FontPtr CreateNew(const std::string& Name, int size) const;

	   public:
		FontPtr Get(const std::string& Name, int size);
	};

	static StaticFontLoader loader;

   public:
	/**
	 * Constructor.
	 *
	 * @param filename the filename of the True Type Font.
	 * @param size the size the font should be in.
	 */
	SDLTrueTypeFont(TTF_Font* font);

	/**
	 * Destructor.
	 */
	virtual ~SDLTrueTypeFont();

	/**
	 * Sets the spacing between rows in pixels. Default is 0 pixels.
	 * The spacing can be negative.
	 *
	 * @param spacing the spacing in pixels.
	 */
	virtual void setRowSpacing(int spacing);

	/**
	 * Gets the spacing between rows in pixels.
	 *
	 * @return the spacing.
	 */
	virtual int getRowSpacing();

	/**
	 * Sets the spacing between letters in pixels. Default is 0 pixels.
	 * The spacing can be negative.
	 *
	 * @param spacing the spacing in pixels.
	 */
	virtual void setGlyphSpacing(int spacing);

	/**
	 * Gets the spacing between letters in pixels.
	 *
	 * @return the spacing.
	 */
	virtual int getGlyphSpacing();


	// Inherited from Font

	virtual Vector2D getSize(const std::string& text, ELanguage language) const override;

	virtual int getHeight(ELanguage language) const override;

	static FontPtr Get(const std::string& fontName, int size);

	static void DestroyFontLibrary();

	TTF_Font* SDL_Font(ELanguage lang = ELanguage::English) const;

	void setFontOverride(TTF_Font* font, ELanguage lang);

   protected:
	TTF_Font* mFont = NULL;
	std::map<ELanguage, TTF_Font*> mFontOverrides;

	int mGlyphSpacing = 0;
	int mRowSpacing = 0;
};
