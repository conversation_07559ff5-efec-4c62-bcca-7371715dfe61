#pragma once

#include <Json.h>

#include "RequestDto.h"

class CardDto
{
    public:
        // members
        std::string CardName;
        float Confidence;
        int X;
        int Y;
        int Width;
        int Height;
        std::string RegionId;

        // constructor
        CardDto(const std::string &cardName, const float confidence, const int x, const int y, const int width, const int height, const std::string &regionId) : CardName(std::string(cardName)), Confidence(confidence), X(x), Y(y), Width(width), Height(height), RegionId(std::string(regionId)) {};

        // methods
        json ToJSON() const;
        CardDto& operator=(const CardDto& other) = default;
};

class DetectedCardsEventDto : public RequestDto
{
    public:
        // members
        std::vector<CardDto> Cards;

        // constructors
        explicit DetectedCardsEventDto(const std::vector<CardDto>& cards);
        DetectedCardsEventDto() = default;

        // methods
        json ToEvent() const;
        json ToJSON() const override;
        bool operator==(const DetectedCardsEventDto& other) const = default;
};