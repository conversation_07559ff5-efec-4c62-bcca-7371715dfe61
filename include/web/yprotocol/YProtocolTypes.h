#pragma once

#include <chrono>
#include <functional>
#include <future>
#include <list>
#include <map>
#include <memory>
#include <queue>
#include <stdexcept>
#include <string>

#include "Enums.h"
#include "Logger.h"
#include "TConfiguration.h"
#include "ThreadSafeProperty.h"
#include "YDelegate.h"
#include "YProtocolSharedTypes.h"

DECLARE_LOG_CATEGORY(LogYProtocol, Normal)

namespace yprotocol
{

struct EventTemplate
{
	const std::string Name;
	const std::string Description;
	const JsonSchema ContextSchema;

	EventTemplate(const std::string& name, const std::string& description, const JsonSchema& contextSchema = {});
};

class Event
{
   private:
	std::string mName;
	int64_t mTriggeredTS;
	json mData;

   public:
	Event(const EventTemplate& event) noexcept;
	Event(const EventTemplate& event, const json& data) noexcept;

	json AsJsonObject() const noexcept;

	const json& Data() const noexcept;

	// Timestamp of when the event was triggered
	int64_t Triggered() const noexcept;

	const std::string& Name() const noexcept;
};

class EventInterface
{
   protected:
	virtual void OnEventTriggered(const Event& event) = 0;

   public:
	void TriggerEvent(const Event& event);
	inline void TriggerEvent(const EventTemplate& event, const json& context) { TriggerEvent(Event(event, context)); }
};

class Response : public BaseMessage<EClientMessageType>
{
   public:
	Response();
	Response(const BaseMessage& baseMsg);
	uint64_t QuestionID = 0;
	json Answer;
};

class MessageParseError : public BaseMessageParseError
{
   private:
	const uint32_t mContextID;

   public:
	/// Instantiate a MessageParseError instance with the given message.
	/// @param what The message to associate with this error.
	/// @param context The optional context to provide to the client
	MessageParseError(const BaseMessage<EClientMessageType>& message, const std::string& what, const json& context = {});

	uint32_t ContextID() const;
};

class ResponseError : public MessageParseError
{
   private:
	Response mResponse;

   public:
	/// Instantiate a RequestError instance with the given message.
	/// @param request The request this error relates to
	/// @param what The message about what went wrong
	ResponseError(const Response& request, const std::string& what);
	ResponseError(const Response& request, const std::string& what, const json& context);

	const Response& Source() const;
};

class InitError : public std::runtime_error
{
   private:
	const json mContext;

   public:
	/// Instantiate an InitError instance with the given message.
	/// @param what The message to associate with this error.
	/// @param context The optional context to provide to the client
	InitError(const std::string& what, const json& context = {}, bool dropClient = true);

	const json& Context() const;
};

class InternalError : public std::runtime_error
{
   private:
   public:
	/// Instantiate an InternalError instance with the given message.
	/// @param what The message to associate with this error.
	/// @param context The optional context to provide to the client
	InternalError(const std::string& what);
};

inline bool IsTimedOut(uint64_t timestamp, uint64_t timeoutMS, uint64_t time)
{
	return (timestamp + timeoutMS < time);
}

inline bool IsTimedOut(uint64_t timestamp, uint64_t timeoutMS)
{
	return (timestamp + timeoutMS < Timestamp());
}

class YBanRule : public TConfiguration
{
   public:
	uint64_t Duration = 0;
	std::string Name;

	virtual bool ShouldBan() const = 0;

	virtual void OnConfigLoaded(const std::filesystem::path& filename) override;

	YBanRule();
	YBanRule(const std::string& name, const std::string& desc);

	virtual ~YBanRule() {}
};

class IntervalBanRule : public YBanRule
{
   public:
	uint64_t MaxTimestamps = 0;
	uint64_t IntervalDuration = 0;
	std::list<uint64_t> RelevantTimestamps;

	virtual bool ShouldBan() const override;

	virtual void OnConfigLoaded(const std::filesystem::path& filename) override;

	void NewOccurance();

	IntervalBanRule();
	IntervalBanRule(const std::string& name, const std::string& desc);
};

class TCookie
{
   public:
	std::string Name;
	std::string Value;
	std::string Path;
	std::vector<std::string> Options;

	TCookie() {};
	TCookie(const std::string& cookieName, const std::string& value, const std::vector<std::string>& options, const std::string& path = "/");

	json AsJson() const;
};

DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnCookieSet, const TCookie&)
class TCookies
{
   private:
	ThreadSafeProperty<std::map<std::string, std::string>> mCookies;

   public:
	void SetCookieList(const std::string& cookieList);

	std::string Get(const std::string& cookieName, const std::string& notFound = {}) const;

	void Set(const std::string& cookieName, const std::string& value, const std::vector<std::string>& options = {}, const std::string& path = "/");

	TCookies& operator=(const TCookies& rhs);

	FOnCookieSet OnCookieSet;
};

struct Question
{
	std::string QuestionType;
	std::string Topic;
	std::string Message;

	Question(const std::string& topic, const std::string& message);
	virtual ~Question() {};

	virtual json AsJson() const;

	virtual bool IsValidAnswer(const json& answer) const = 0;
};

struct InputQuestion : public Question
{
	enum EInputType : uint8_t
	{
		TEXT,
		INTEGRAL,
		REAL
	};
	EInputType InputStyle = TEXT;

	InputQuestion(const std::string& topic, const std::string& message);

	virtual json AsJson() const override;
	virtual bool IsValidAnswer(const json& answer) const override;
};

struct ChoiceQuestion : public Question
{
	unsigned int MaxChoices;
	unsigned int MinChoices;
	struct Choice
	{
		std::string Title;
		std::string Description;
		json Data;
	};
	std::vector<Choice> Choices;

	virtual json AsJson() const override;
	virtual bool IsValidAnswer(const json& answer) const override;

	ChoiceQuestion(const std::string& topic, const std::string& message, const std::vector<Choice>& choices, int minChoices = 1, int maxChoices = 1);
};

typedef std::shared_ptr<Question> QuestionPtr;

}    // namespace yprotocol