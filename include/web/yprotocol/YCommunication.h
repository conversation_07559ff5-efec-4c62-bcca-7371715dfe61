#pragma once

#include <boost/asio.hpp>
#include <utility>

#include "Future.h"
#include "Logger.h"
#include "ThreadSafeProperty.h"
#include "YDelegate.h"
#include "web/ImaxaConnection.h"
#include "web/yprotocol/YProtocolSharedTypes.h"

DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnEvent, const std::string&, const json&)
DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnInitialized, const json&)

BETTER_ENUM(EMessageStatus, uint8_t, Unsent, TimedOut, Cancelled, ResponseOk, ResponseError);

struct YResponse
{
	EMessageStatus Status;
	yprotocol::BaseMessage<yprotocol::EServerMessageType> Message;
	std::string ErrorMessage() const;

   private:
	YResponse(EMessageStatus status) : Status(status) {}
	YResponse(yprotocol::BaseMessage<yprotocol::EServerMessageType> msg) :
	    Status(msg.Type() == yprotocol::EServerMessageType::Error ? EMessageStatus::ResponseError : EMessageStatus::ResponseOk), Message(std::move(msg))
	{
	}

	friend class YCommunication;
	friend class TCMSYClient;
};

class YCommunication : virtual public logs::LoggingComponent
{
   private:
	std::optional<EVerbosity> PrintAllTrafficVerbosity;

   public:
	static const uint32_t DEFAULT_RESPONSE_TIMEOUT_SECONDS = 5;

	YCommunication(const std::string& name = {});

	// Returns the current contextID used by this client when communicating with the server
	uint64_t ContextID(bool bAdvance = false);

	void ProcessMessage(const std::string& msg);

	// Returns true if connection with yserver is established
	bool Connected() const noexcept;

	// Gets the unique identifier attributed to this client by the server
	const std::string& UID() const;

	// stops the connection
	virtual void Stop();

	void SetPrintTraffic(const std::optional<EVerbosity>& verbosity) { PrintAllTrafficVerbosity = verbosity; }

	// Sends a message to yserver and automatically increments the context id
	boost::future<YResponse> SendNewMessage(yprotocol::EClientMessageType type, json payload = {}, uint32_t timeoutSeconds = DEFAULT_RESPONSE_TIMEOUT_SECONDS) noexcept;

	// Constructs a message and sends it to the server
	boost::future<YResponse> SendMessage(yprotocol::EClientMessageType type, uint64_t contextID, json payload = {},
	                                     uint32_t timeoutSeconds = DEFAULT_RESPONSE_TIMEOUT_SECONDS) noexcept;

	// Sends a new message to yserver of type "request" and creates a request body that targets the requestName method
	boost::future<YResponse> Request(const std::string& requestName, json data = {}, uint32_t timeoutSeconds = DEFAULT_RESPONSE_TIMEOUT_SECONDS) noexcept;

	void CloseConnection(const std::string& reason);

	// Delegate to bind event handlers to
	FOnEvent OnEvent;

	// Delegate that gets fired when the initialization step is complete (connection established)
	FOnInitialized OnInitialized;

   protected:
	enum class ECommunicationState : uint8_t
	{
		Closed,
		Connecting,
		WaitingForInit,
		Connected,
		Authenticated
	};

	// returns the previous communication state
	ECommunicationState SetCommunicationState(ECommunicationState coms, const std::error_code& ec = {});

	ECommunicationState WaitConnecting(std::error_code* outError = NULL);

	void WaitForDisconnect();

	std::atomic<uint64_t> mContextID = 1;
	std::string mUID;
	websocketpp::frame::opcode::value mMessageMode = websocketpp::frame::opcode::TEXT;
	bool bLogCommunicationErrors = true;

	void ClearMessageBuffer() noexcept;

	bool SendMessageInternal(yprotocol::EClientMessageType type, uint64_t contextID, boost::promise<YResponse>& response, json payload = {}) const noexcept;

	virtual imaxa_connection_ptr GetConnection() const = 0;
	virtual boost::asio::io_context& GetIO() = 0;

   private:
	struct BufferedMessage
	{
		yprotocol::EClientMessageType Type = yprotocol::EClientMessageType::Request;
		boost::promise<YResponse> Promise;
		uint64_t ContextID;
		json Payload;
		uint32_t TimeoutSeconds;
	};
	struct HandlerContainer
	{
		boost::promise<YResponse> Promise;
		std::unique_ptr<boost::asio::deadline_timer> Timer;

		HandlerContainer(const HandlerContainer&) = delete;
		HandlerContainer(YCommunication& coms, yprotocol::EClientMessageType type, uint64_t contextID, const json& payload, uint32_t timeoutSeconds);
		HandlerContainer(YCommunication& coms, BufferedMessage& msg);
	};
	ThreadSafeProperty<std::map<uint64_t, HandlerContainer>, std::mutex> Handlers;
	ThreadSafeProperty<std::list<BufferedMessage>, std::mutex> BufferedMessages;

	std::error_code mLastErrorCode;
	ThreadSafeProperty<ECommunicationState, std::shared_mutex> mCommunicationState = ECommunicationState::Closed;
	std::condition_variable_any CommunicationStateCV;

	static json formMessage(yprotocol::EClientMessageType type, uint64_t contextID, json payload = {}) noexcept;

	void onTimedOut(yprotocol::EClientMessageType type, uint64_t contextID, const json& payload, const boost::system::error_code& ec);
};