#pragma once

#include <boost/asio/basic_waitable_timer.hpp>

#include "AsyncTaskRunner.h"
#include "Logger.h"
#include "ThreadSafeProperty.h"
#include "YDelegate.h"
#include "YObject.h"
#include "YProtocolRequest.h"
#include "web/ImaxaConnection.h"
#include "web/KeycloakServerSettings.h"

namespace yprotocol
{

// server-side representation of itself
class YProtocol : virtual public AsyncTaskRunner
{
   private:
	ThreadSafeProperty<std::map<imaxa_connection_hdl, std::shared_ptr<YProtocolClient>, std::owner_less<imaxa_connection_hdl>>, std::shared_mutex> AllClients;

	void OnRawMessage(YProtocolClient* client, imaxa_connection_hdl_ref hdl, const message_ptr& msg);

   protected:
	uint32_t mMaxLatency = 0;
	websocketpp::frame::opcode::value mMessageMode = websocketpp::frame::opcode::TEXT;

	security::KeycloakSettings mKeycloakSettings;
	security::TokenVerifySettings mTokenVerificationSettings;
	ThreadSafeProperty<std::optional<security::TokenVerificationContext>, std::shared_mutex> mTokenVerifier;
	bool bTokenVerificationContextRetrievalInProgress = false;

	std::optional<EVerbosity> PrintAllTrafficVerbosity;

	bool CreateJWTVerificationContext();

   public:
	YProtocol();
	virtual ~YProtocol();

	virtual std::error_code VerifyToken(std::optional<security::JWToken> token);

	// This method pings all websocket clients on the server.
	// Intended to be called in OnPingTimer of a class deriving from WebServer.
	// It goes through the list of all clients and pings them (using the ping frame of the websocket protocol).
	// If a client does not respond to a ping before a new ping is set, the client will be automatically disconnected.
	void PingClients();

	void CloseAllConnections();

	void SetPrintTraffic(const std::optional<EVerbosity>& verbosity) { PrintAllTrafficVerbosity = verbosity; }

	void SendMessage(imaxa_connection_hdl_ref hdl, uint64_t contextID, EServerMessageType type, const json& payload);

	virtual void OnMessage(YProtocolClient* client, const std::string& msg);

	bool Connect(const std::shared_ptr<YProtocolClient>& client);
	virtual void OnClientDisconnected(YProtocolClient& client);

	void Broadcast(const Event& event);
	void Broadcast(const std::list<std::shared_ptr<YProtocolClient>>& toClients, const Event& event, const std::string& queueID);

	IntervalBanRule MaxMessageRate = IntervalBanRule("max-message-rate", "The maximum number of messages allowed for the client in a given interval");

	static json formMessage(EServerMessageType type, uint64_t contextID, const json& payload);

	template <typename ErrorEnum>
	static json make_error_object(ErrorEnum error, const std::string& errorType, const std::string& errorMessage, const json& context = {})
	    requires(!std::is_same_v<ErrorEnum, uint16_t>)
	{
		return make_error_object(static_cast<uint16_t>(error), errorType, errorMessage, context);
	}

	static json make_error_object(uint16_t errorCode, const std::string& errorType, const std::string& errorMessage, const json& context = {});
};

DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnDisconnected);

enum class EQuestionResult : uint8_t
{
	OK = 0,
	ASK_AGAIN = 1
};

// server-side representation of a client
class YProtocolClient :
    public Lockable<>,
    virtual public logs::LoggingComponent,
    public EventInterface,
    public UniquelyIdentifiableObject,
    public web::PingableConnection,
    public std::enable_shared_from_this<YProtocolClient>
{
   public:
	typedef std::function<EQuestionResult(YProtocolClient&, const QuestionPtr&, const json&, json&)> AnswerHandler;

   private:
	imaxa_condition_variable_any mConnectionStateCV;
	ThreadSafeProperty<web::websockets::session::state::value, std::shared_mutex> mConnectionState = web::websockets::session::state::closed;
	imaxa_connection_hdl ConnectionHdl;
	std::string mConnectError;
	std::optional<security::JWToken> mToken;
	std::shared_ptr<boost::asio::basic_waitable_timer<std::chrono::system_clock>> mTokenExpiryTimer;

	std::atomic<uint64_t> mServerContextID = 0;

	std::atomic<uint64_t> mLastMessageTS = 0;
	ThreadSafeProperty<std::queue<std::pair<QuestionPtr, AnswerHandler>>> mQuestions;
	std::atomic<uint64_t> mLastQuestionID = 0;
	std::atomic<bool> bSentQuestion = false;

   protected:
	YProtocol* const Server;

	virtual void OnConnectionOpened() {}
	virtual void OnConnectionClosed() {}

	// begin EventInterface
	virtual void OnEventTriggered(const Event& event) override;
	// end EventInterface
   public:
	const std::shared_ptr<class YRequestHandler> RequestHandler;

	YProtocolClient(YProtocol* server, std::shared_ptr<class YRequestHandler> requestHandler, const std::string& uid, std::optional<security::JWToken> token);
	virtual ~YProtocolClient();

	void Reply(const Request& req, const json& response);
	void ReplyError(const Request& req, const std::string& message, const json& ctx = json());
	void ReplyError(const Request& req, uint16_t error, const std::string& scope, const std::string& message, const json& ctx = json());

	void SendMessage(uint64_t contextID, EServerMessageType type, const json& payload, bool terminateConnection = false) noexcept;
	void SendNewServerMessage(EServerMessageType type, const json& payload) noexcept;
	void SendError(uint64_t contextID, const json& error, bool terminateConnection) noexcept;

	uint64_t ServerContext(bool bIncrement = false);

	void StartConnect(const imaxa_connection_ptr& con);

	bool WaitUntilConnected();

	const std::string& GetConnectionError() const;

	void Reconnect(imaxa_connection_hdl_ref hdl);

	bool Connected() const noexcept;

	web::websockets::session::state::value ConnectionState() const noexcept;

	imaxa_connection_hdl ConnectionHandle() const noexcept;

	void Disconnect(websocketpp::close::status::value c, const std::string& msg, bool bWaitForEnd = false, bool bOnlyIfConnected = false) noexcept;

	void Ask(const QuestionPtr& question, const AnswerHandler& answerHandler);

	json AnswerReceived(const Response& response);

	void UpdateLastMessage(const BaseMessage<EClientMessageType>& message);

	uint64_t LastMessageFromClient() const;

	void ClearQuestions();

	void HandleMessage(const BaseMessage<EClientMessageType>& message);

	std::optional<security::JWToken> Token() const;

	void SetToken(std::optional<security::JWToken> token);

	void OnClose(imaxa_connection_hdl_ref hdl);
	void OnPong(imaxa_connection_hdl_ref hdl, const std::string& payload);

	IntervalBanRule MessageRate;

	FOnDisconnected OnDisconnected;
};

struct FRequestExecutionOptions
{
	bool bLockRequired = true;
	bool bAsync = false;
};

class YRequestHandler
{
   public:
	typedef std::function<json(YProtocolClient&, const Request&)> YRequestHandlerMethod;
	class MethodInfo : public TConfiguration
	{
	   protected:
		MethodInfo() {}

	   public:
		MethodInfo(const YRequestHandlerMethod& handler, const std::string& description, const JsonSchema& parameterSchema,
		           const std::map<std::string, JsonSchema>& responseSchemas, const FRequestExecutionOptions& options = {}) :
		    TConfiguration(parameterSchema), Description(description), Handler(handler), Execution(options), PossibleResponses(responseSchemas)
		{
		}
		const std::string Description;
		const YRequestHandlerMethod Handler;
		const FRequestExecutionOptions Execution;
		const std::map<std::string, JsonSchema> PossibleResponses;

		static const FSchemaPrintOptions MarkdownOptions_Param;
		static const FSchemaPrintOptions MarkdownOptions_Return;

		void Execute(const std::shared_ptr<YProtocolClient>& client, const Request& req) const;

		void CreateMarkdownDocs(std::ostream& output, const std::string& title, size_t indent) const;
	};

	struct FRequestHandlerInfo
	{
		const MethodInfo* Method = NULL;
		std::string Error;
	};

   private:
	std::map<std::string, MethodInfo> mMethods;
	std::map<std::string, std::shared_ptr<const EventTemplate>> mEvents;

   public:
	virtual ~YRequestHandler() {}

	const std::shared_ptr<const EventTemplate>& RegisterEvent(const std::string& eventName, const std::string& description, const JsonSchema& schema = {});

	void RegisterMethod(const std::string& name, const std::string& description, const YRequestHandlerMethod& method, const JsonSchema& parameterSchema = JsonSchema(),
	                    const std::map<std::string, JsonSchema>& responseSchemas = {}, const FRequestExecutionOptions& options = {});

	const std::map<std::string, MethodInfo>& Methods() const { return mMethods; }

	const std::map<std::string, std::shared_ptr<const EventTemplate>>& Events() const { return mEvents; }

	virtual FRequestHandlerInfo GetRequestHandlerFor(const Request& request, const YProtocolClient& client) const noexcept;
};

template <class T, class Base = YRequestHandler>
class YDedicatedRequestHandler : public Base
{
   public:
	typedef std::function<json(T&, const Request&)> YDedicatedRequestHandlerMethod;

   protected:
	typedef YDedicatedRequestHandler<T, Base> DedicatedRequestHandler;

   public:
	using Base::Base;

	virtual ~YDedicatedRequestHandler() {}

	json HandleRequest(const Request& request, T& client) const { return Base::HandleRequest(request, static_cast<YProtocolClient&>(client)); }

	void RegisterMethod(const std::string& name, const std::string& description, const YDedicatedRequestHandlerMethod& method,
	                    const JsonSchema& parameterSchema = JsonSchema(), const std::map<std::string, JsonSchema>& responseSchemas = {},
	                    const FRequestExecutionOptions& options = {})
	{
		Base::RegisterMethod(
		  name, description, [method](YProtocolClient& client, const Request& req) -> json { return method(static_cast<T&>(client), req); }, parameterSchema,
		  responseSchemas, options);
	}
};

}    // namespace yprotocol