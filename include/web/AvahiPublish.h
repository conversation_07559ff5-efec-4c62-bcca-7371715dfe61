#pragma once

#include <boost/asio/ip/address.hpp>
#include <cstdint>
#include <list>
#include <string>
#include <thread>
#include <vector>

#include "AvahiCommon.h"
#include "Logger.h"

class AvahiClient;
class AvahiEntryGroup;
class AvahiSimplePoll;

DECLARE_LOG_CATEGORY(LogAvahiPublisher, Normal)

namespace avahi
{

struct ServiceDataChannel
{
	web::features::value type = 0;
	uint16_t port = 0;
	std::string interface = {};    // The network interfaces this service is available on (empty for all interfaces)
	bool allow_ipv4 = true;    // whether to list service as IPv4-compatible
	bool allow_ipv6 = true;    //  whether to list service as IPv6-compatible
};

struct ServerConfig
{
	std::string service_name; /**< The service name to publish */
	bool add_hostname_to_service_name = false;
	std::list<ServiceDataChannel> channels;
};

class Publisher
{
   private:
	AvahiClient* Client = NULL;
	AvahiEntryGroup* Group = NULL;
	AvahiSimplePoll* SimplePoll = NULL;
	std::thread PublishPollThread;
	std::string ServiceName;

	void create_services(AvahiClient* c);
	void entry_group_callback(AvahiEntryGroup* g, int state);
	void client_callback(AvahiClient* c, int state);

	friend class PublisherAccessor;

   public:
	ServerConfig Config;

	~Publisher();

	void StartPublish();
	void EndPublish();
};

};    // namespace avahi
