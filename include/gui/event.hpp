#pragma once

class Widget;

/**
 * Base class for all events.
 *
 * <AUTHOR> @since 0.6.0
 */
class Event
{
   public:
	/**
	 * Constructor.
	 *
	 * @param source the source widget of the event.
	 */
	Event(Widget* source) : mSource(source) {}

	Event() {}

	/**
	 * Destructor.
	 */
	virtual ~Event() {}

	/**
	 * Gets the source widget of the event.
	 *
	 * @return the source widget of the event.
	 */
	Widget* getSource() const { return mSource; }

	bool isSource(Widget* source) const { return source == mSource; }


   protected:
	Widget* mSource = nullptr;
	unsigned int mType = 0;
};