#pragma once

#include <string>

#include "gui/inputevent.hpp"

class Widget;

/**
 * Key event.
 */
class TextEvent : public InputEvent
{
   public:
	/**
	 * Constructor.
	 *
	 * @param source the source widget of the event.
	 * @param isShiftPressed true if shift is pressed, false otherwise.
	 * @param isControlPressed true if control is pressed, false otherwise.
	 * @param isAltPressed true if alt is pressed, false otherwise.
	 * @param isMetaPressed true if meta is pressed, false otherwise.
	 * @param type the type of the event.
	 * @param isNumericPad true if the event occurred on the numeric pad,
	 *                     false otherwise.
	 * @param key represents the key of the event.
	 */
	TextEvent(Widget* source, bool isShiftPressed, bool isControlPressed, bool isAltPressed, bool isMetaPressed, const std::string& text);

	/**
	 * Gets the text of the event.
	 *
	 * @return the text of the event.
	 */
	const std::string& getText() const;

   protected:
	std::string mText;
};
